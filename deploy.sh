#!/bin/bash

# 快乐8数据分析系统部署脚本
# 使用方法: ./deploy.sh [dev|prod|docker]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装"
        exit 1
    fi
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    check_command python3
    check_command pip3
    
    python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    if [[ $(python3 -c "import sys; print(sys.version_info >= (3, 8))") == "False" ]]; then
        log_error "需要Python 3.8或更高版本"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    pip3 install -r requirements.txt
    log_success "依赖安装完成"
}

# 检查数据文件
check_data() {
    if [ ! -f "happy8_data.csv" ]; then
        log_warning "数据文件不存在"
        read -p "是否要运行爬虫获取数据? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "运行爬虫获取数据..."
            python3 happy8_crawler.py
            log_success "数据获取完成"
        fi
    else
        log_success "数据文件存在"
    fi
}

# 开发环境部署
deploy_dev() {
    log_info "部署到开发环境..."
    check_python
    install_dependencies
    check_data
    
    export ENVIRONMENT=development
    log_info "启动开发服务器..."
    python3 start_server.py
}

# 生产环境部署
deploy_prod() {
    log_info "部署到生产环境..."
    check_python
    install_dependencies
    check_data
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p data
    
    export ENVIRONMENT=production
    log_info "启动生产服务器..."
    python3 main.py
}

# Docker部署
deploy_docker() {
    log_info "Docker部署..."
    check_command docker
    check_command docker-compose
    
    # 创建必要的目录
    mkdir -p data
    mkdir -p logs
    
    # 检查数据文件
    if [ ! -f "happy8_data.csv" ]; then
        log_warning "数据文件不存在，将在容器内运行爬虫"
    fi
    
    log_info "构建Docker镜像..."
    docker-compose build
    
    log_info "启动Docker容器..."
    docker-compose up -d
    
    log_success "Docker部署完成"
    log_info "服务地址: http://localhost:8000"
    log_info "查看日志: docker-compose logs -f"
    log_info "停止服务: docker-compose down"
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    
    # 停止Docker服务
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
    fi
    
    # 停止Python进程
    pkill -f "python.*main.py" || true
    pkill -f "python.*start_server.py" || true
    
    log_success "服务已停止"
}

# 查看状态
check_status() {
    log_info "检查服务状态..."
    
    # 检查Docker容器
    if command -v docker &> /dev/null; then
        docker_status=$(docker-compose ps 2>/dev/null || echo "未运行")
        echo "Docker状态: $docker_status"
    fi
    
    # 检查Python进程
    python_processes=$(pgrep -f "python.*main.py\|python.*start_server.py" || echo "")
    if [ -n "$python_processes" ]; then
        echo "Python进程: $python_processes"
    else
        echo "Python进程: 未运行"
    fi
    
    # 检查端口
    if command -v netstat &> /dev/null; then
        port_status=$(netstat -tlnp | grep :8000 || echo "端口8000未监听")
        echo "端口状态: $port_status"
    fi
}

# 显示帮助
show_help() {
    echo "快乐8数据分析系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令]"
    echo ""
    echo "命令:"
    echo "  dev     - 开发环境部署"
    echo "  prod    - 生产环境部署"
    echo "  docker  - Docker部署"
    echo "  stop    - 停止服务"
    echo "  status  - 查看状态"
    echo "  help    - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 开发环境部署"
    echo "  $0 docker   # Docker部署"
    echo "  $0 stop     # 停止所有服务"
}

# 主函数
main() {
    case "${1:-help}" in
        "dev")
            deploy_dev
            ;;
        "prod")
            deploy_prod
            ;;
        "docker")
            deploy_docker
            ;;
        "stop")
            stop_service
            ;;
        "status")
            check_status
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"

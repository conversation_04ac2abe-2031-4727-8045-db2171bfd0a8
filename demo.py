#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据分析系统演示脚本
展示系统的主要功能
"""

import requests
import json
import time
import os

def test_api_endpoints():
    """测试API接口"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试API接口...")
    print("=" * 50)
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康检查: {health_data['status']}")
            print(f"   服务: {health_data['service']}")
            print(f"   版本: {health_data['version']}")
            print(f"   数据已加载: {health_data['data_loaded']}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保服务已启动: python app.py")
        return False
    
    # 测试基本统计
    try:
        response = requests.get(f"{base_url}/api/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 基本统计:")
            print(f"   总期数: {stats['total_periods']}")
            print(f"   数据跨度: {stats['date_range']['days']} 天")
            print(f"   时间范围: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
        else:
            print(f"❌ 获取统计信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取统计信息错误: {e}")
    
    # 测试频率数据
    try:
        response = requests.get(f"{base_url}/api/frequency?limit=5", timeout=5)
        if response.status_code == 200:
            frequency = response.json()
            print(f"✅ 号码频率 (TOP 5):")
            for item in frequency:
                print(f"   号码 {item['number']:02d}: {item['frequency']}%")
        else:
            print(f"❌ 获取频率数据失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取频率数据错误: {e}")
    
    # 测试最新结果
    try:
        response = requests.get(f"{base_url}/api/results?limit=2", timeout=5)
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 最新开奖结果 (最近2期):")
            for result in results:
                numbers_str = ' '.join([f"{num:02d}" for num in result['numbers']])
                print(f"   期号 {result['period']} ({result['date']}): {numbers_str}")
        else:
            print(f"❌ 获取开奖结果失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取开奖结果错误: {e}")
    
    print("\n🎯 API测试完成!")
    return True

def show_system_info():
    """显示系统信息"""
    print("🎯 快乐8数据分析系统演示")
    print("=" * 50)
    
    # 检查文件
    files_to_check = [
        'app.py', 'data_service.py', 'happy8_crawler.py',
        'templates/index.html', 'happy8_data.csv'
    ]
    
    print("📁 文件检查:")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size:,} bytes)")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    print()

def show_features():
    """展示功能特点"""
    print("🌟 系统功能特点:")
    print("=" * 50)
    
    features = [
        "🎯 数据爬取: 自动获取最新500期开奖数据",
        "📊 数据分析: 号码频率、趋势分析、分布统计",
        "🌐 Web界面: 美观的响应式Web界面",
        "📈 可视化: 交互式图表展示",
        "🔄 实时更新: 一键刷新最新数据",
        "📱 移动友好: 支持手机和平板访问",
        "🛠️ API接口: 完整的RESTful API",
        "🎨 现代设计: Bootstrap + 渐变色设计"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print()

def show_usage_guide():
    """显示使用指南"""
    print("📖 使用指南:")
    print("=" * 50)
    
    print("1. 🚀 启动系统:")
    print("   python start_server.py  # 一键启动")
    print("   或")
    print("   python app.py          # 手动启动")
    print()
    
    print("2. 🌐 访问界面:")
    print("   主页: http://localhost:8000")
    print("   API文档: http://localhost:8000/docs")
    print()
    
    print("3. 📊 主要功能:")
    print("   - 查看最新开奖结果")
    print("   - 分析号码出现频率")
    print("   - 查看趋势变化")
    print("   - 查看分布统计")
    print("   - 一键刷新数据")
    print()

def main():
    """主函数"""
    show_system_info()
    show_features()
    show_usage_guide()
    
    # 询问是否测试API
    print("🧪 是否要测试API接口? (需要先启动服务)")
    choice = input("输入 y 测试，其他键跳过: ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        print()
        success = test_api_endpoints()
        if success:
            print("\n🎉 所有测试通过!")
            print("💡 提示: 访问 http://localhost:8000 查看完整的Web界面")
        else:
            print("\n⚠️  请先启动服务:")
            print("   python start_server.py")
    
    print("\n" + "=" * 50)
    print("📚 更多信息请查看 README.md")
    print("🐛 问题反馈请联系开发者")
    print("=" * 50)

if __name__ == "__main__":
    main()

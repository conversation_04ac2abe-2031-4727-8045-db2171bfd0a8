#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os
from typing import Optional

class Config:
    """基础配置"""
    # 应用配置
    APP_NAME = "快乐8数据分析系统"
    VERSION = "1.0.0"
    DEBUG = False
    
    # 服务器配置
    HOST = "0.0.0.0"
    PORT = 8000
    WORKERS = 1
    
    # 数据配置
    DATA_FILE = "happy8_data.csv"
    CACHE_TIMEOUT = 600  # 10分钟缓存
    REFRESH_TIMEOUT = 600  # 10分钟刷新限制
    
    # 爬虫配置
    CRAWLER_TIMEOUT = 30
    CRAWLER_RETRY = 3
    DEFAULT_PERIODS = 500
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = "DEBUG"
    WORKERS = 1

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = "WARNING"
    WORKERS = 4  # 根据CPU核心数调整
    
    # 从环境变量获取配置
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 8000))
    WORKERS = int(os.getenv("WORKERS", 4))
    
    # 安全配置
    CACHE_TIMEOUT = int(os.getenv("CACHE_TIMEOUT", 600))  # 生产环境缓存10分钟

class DockerConfig(ProductionConfig):
    """Docker环境配置"""
    HOST = "0.0.0.0"
    PORT = 8000
    WORKERS = int(os.getenv("WORKERS", 2))

# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'docker': DockerConfig,
}

def get_config(env: Optional[str] = None) -> Config:
    """获取配置"""
    if env is None:
        env = os.getenv('ENVIRONMENT', 'development')
    
    return config_map.get(env, DevelopmentConfig)()

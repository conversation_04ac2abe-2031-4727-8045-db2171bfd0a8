#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清理工具
清理现有数据，确保每期只有20个号码
"""

import pandas as pd
import json
import os
from typing import List

def clean_csv_data(input_file: str = 'happy8_data.csv', output_file: str = None):
    """
    清理CSV数据文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则覆盖原文件
    """
    if not os.path.exists(input_file):
        print(f"文件 {input_file} 不存在")
        return False
    
    if output_file is None:
        output_file = input_file
    
    print(f"开始清理数据文件: {input_file}")
    
    try:
        # 读取数据
        df = pd.read_csv(input_file)
        print(f"原始数据: {len(df)} 期")
        
        cleaned_count = 0
        invalid_count = 0
        
        # 清理每一行数据
        for index, row in df.iterrows():
            try:
                # 解析号码列表
                numbers = eval(row['numbers'])
                original_count = len(numbers)
                
                # 如果超过20个号码，从后往前取20个
                if len(numbers) > 20:
                    numbers = numbers[-20:]
                    cleaned_count += 1
                    print(f"期号 {row['period']}: {original_count} -> {len(numbers)} 个号码")
                
                # 如果少于15个号码，标记为无效
                elif len(numbers) < 15:
                    invalid_count += 1
                    print(f"期号 {row['period']}: 只有 {len(numbers)} 个号码，可能无效")
                
                # 更新数据
                df.at[index, 'numbers'] = str(numbers)
                df.at[index, 'numbers_str'] = ' '.join([f"{int(num):02d}" for num in numbers])
                
            except Exception as e:
                print(f"处理期号 {row['period']} 时出错: {e}")
                invalid_count += 1
        
        # 保存清理后的数据
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n清理完成:")
        print(f"- 总期数: {len(df)}")
        print(f"- 清理期数: {cleaned_count}")
        print(f"- 可能无效期数: {invalid_count}")
        print(f"- 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"清理数据失败: {e}")
        return False

def clean_json_data(input_file: str = 'happy8_data.json', output_file: str = None):
    """
    清理JSON数据文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则覆盖原文件
    """
    if not os.path.exists(input_file):
        print(f"文件 {input_file} 不存在")
        return False
    
    if output_file is None:
        output_file = input_file
    
    print(f"开始清理JSON数据文件: {input_file}")
    
    try:
        # 读取数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"原始数据: {len(data)} 期")
        
        cleaned_count = 0
        invalid_count = 0
        
        # 清理每一期数据
        for item in data:
            try:
                numbers = item['numbers']
                original_count = len(numbers)
                
                # 如果超过20个号码，从后往前取20个
                if len(numbers) > 20:
                    numbers = numbers[-20:]
                    cleaned_count += 1
                    print(f"期号 {item['period']}: {original_count} -> {len(numbers)} 个号码")
                
                # 如果少于15个号码，标记为无效
                elif len(numbers) < 15:
                    invalid_count += 1
                    print(f"期号 {item['period']}: 只有 {len(numbers)} 个号码，可能无效")
                
                # 更新数据
                item['numbers'] = numbers
                item['numbers_str'] = ' '.join([f"{int(num):02d}" for num in numbers])
                
            except Exception as e:
                print(f"处理期号 {item.get('period', 'unknown')} 时出错: {e}")
                invalid_count += 1
        
        # 保存清理后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\n清理完成:")
        print(f"- 总期数: {len(data)}")
        print(f"- 清理期数: {cleaned_count}")
        print(f"- 可能无效期数: {invalid_count}")
        print(f"- 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"清理JSON数据失败: {e}")
        return False

def validate_data(file_path: str):
    """
    验证数据文件的质量
    
    Args:
        file_path: 数据文件路径
    """
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return
    
    print(f"验证数据文件: {file_path}")
    
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
            
            print(f"\n数据概览:")
            print(f"- 总期数: {len(df)}")
            
            # 统计号码数量分布
            number_counts = []
            for _, row in df.iterrows():
                numbers = eval(row['numbers'])
                number_counts.append(len(numbers))
            
            from collections import Counter
            distribution = Counter(number_counts)
            
            print(f"- 号码数量分布:")
            for count, freq in sorted(distribution.items()):
                print(f"  {count}个号码: {freq}期")
            
            print(f"- 平均号码数: {sum(number_counts)/len(number_counts):.1f}")
            print(f"- 标准期数(20个号码): {distribution.get(20, 0)}期")
            
        elif file_path.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n数据概览:")
            print(f"- 总期数: {len(data)}")
            
            # 统计号码数量分布
            number_counts = []
            for item in data:
                number_counts.append(len(item['numbers']))
            
            from collections import Counter
            distribution = Counter(number_counts)
            
            print(f"- 号码数量分布:")
            for count, freq in sorted(distribution.items()):
                print(f"  {count}个号码: {freq}期")
            
            print(f"- 平均号码数: {sum(number_counts)/len(number_counts):.1f}")
            print(f"- 标准期数(20个号码): {distribution.get(20, 0)}期")
        
    except Exception as e:
        print(f"验证数据失败: {e}")

def main():
    """主函数"""
    print("快乐8数据清理工具")
    print("=" * 50)
    
    # 检查数据文件
    csv_exists = os.path.exists('happy8_data.csv')
    json_exists = os.path.exists('happy8_data.json')
    
    if not csv_exists and not json_exists:
        print("未找到数据文件，请先运行爬虫获取数据")
        return
    
    # 验证原始数据
    if csv_exists:
        print("\n验证原始CSV数据:")
        validate_data('happy8_data.csv')
    
    if json_exists:
        print("\n验证原始JSON数据:")
        validate_data('happy8_data.json')
    
    # 询问是否清理
    print("\n" + "=" * 50)
    choice = input("是否要清理数据？(y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        # 备份原始文件
        if csv_exists:
            print("\n备份原始CSV文件...")
            import shutil
            shutil.copy('happy8_data.csv', 'happy8_data_backup.csv')
            print("备份完成: happy8_data_backup.csv")
        
        if json_exists:
            print("\n备份原始JSON文件...")
            import shutil
            shutil.copy('happy8_data.json', 'happy8_data_backup.json')
            print("备份完成: happy8_data_backup.json")
        
        # 清理数据
        if csv_exists:
            print("\n清理CSV数据:")
            clean_csv_data()
        
        if json_exists:
            print("\n清理JSON数据:")
            clean_json_data()
        
        # 验证清理后的数据
        if csv_exists:
            print("\n验证清理后的CSV数据:")
            validate_data('happy8_data.csv')
        
        if json_exists:
            print("\n验证清理后的JSON数据:")
            validate_data('happy8_data.json')
        
        print("\n" + "=" * 50)
        print("数据清理完成！")
        print("原始文件已备份，如有问题可以恢复")
    else:
        print("跳过数据清理")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存机制
"""

import time
import requests
import json

def test_refresh_cache():
    """测试刷新缓存机制"""
    base_url = "http://localhost:8000"
    
    print("=" * 50)
    print("测试刷新缓存机制")
    print("=" * 50)
    
    # 1. 检查刷新状态
    print("1. 检查初始刷新状态...")
    try:
        response = requests.get(f"{base_url}/api/refresh/status")
        status = response.json()
        print(f"   状态: {status}")
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    # 2. 第一次刷新
    print("\n2. 第一次刷新数据...")
    try:
        response = requests.post(f"{base_url}/api/refresh")
        result = response.json()
        print(f"   结果: {result}")
        print(f"   状态码: {response.status_code}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 3. 立即再次尝试刷新（应该被限制）
    print("\n3. 立即再次尝试刷新（应该被限制）...")
    try:
        response = requests.post(f"{base_url}/api/refresh")
        result = response.json()
        print(f"   结果: {result}")
        print(f"   状态码: {response.status_code}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 4. 检查刷新状态
    print("\n4. 检查刷新状态...")
    try:
        response = requests.get(f"{base_url}/api/refresh/status")
        status = response.json()
        print(f"   状态: {status}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n测试完成！")

def test_data_cache():
    """测试数据缓存"""
    base_url = "http://localhost:8000"
    
    print("=" * 50)
    print("测试数据缓存机制")
    print("=" * 50)
    
    # 测试多次请求相同数据的响应时间
    endpoints = [
        "/api/stats",
        "/api/frequency?limit=20",
        "/api/charts/frequency",
        "/api/charts/trends"
    ]
    
    for endpoint in endpoints:
        print(f"\n测试端点: {endpoint}")
        
        # 第一次请求（应该较慢）
        start_time = time.time()
        try:
            response = requests.get(f"{base_url}{endpoint}")
            first_time = time.time() - start_time
            print(f"   第一次请求: {first_time:.3f}s")
        except Exception as e:
            print(f"   第一次请求错误: {e}")
            continue
        
        # 第二次请求（应该较快，使用缓存）
        start_time = time.time()
        try:
            response = requests.get(f"{base_url}{endpoint}")
            second_time = time.time() - start_time
            print(f"   第二次请求: {second_time:.3f}s")
            
            if second_time < first_time:
                print(f"   ✅ 缓存生效，提速 {((first_time - second_time) / first_time * 100):.1f}%")
            else:
                print(f"   ⚠️  缓存可能未生效")
                
        except Exception as e:
            print(f"   第二次请求错误: {e}")

if __name__ == "__main__":
    print("快乐8缓存机制测试")
    print("请确保服务器正在运行 (python app.py)")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试数据缓存
    test_data_cache()
    
    print("\n" + "=" * 50)
    
    # 测试刷新缓存
    test_refresh_cache()

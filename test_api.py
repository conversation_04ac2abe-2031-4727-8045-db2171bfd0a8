#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的号码分析API
"""

import requests
import json

def test_analyze_numbers():
    """测试号码分析API"""
    url = "http://localhost:8000/api/analyze-numbers"
    
    # 测试数据 - 现在可以选择最多10个号码
    test_data = {
        "numbers": [1, 15, 30, 45, 60, 75],
        "periods": 100
    }
    
    try:
        response = requests.post(url, json=test_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API测试成功！")
            print(f"总匹配期数: {result['summary']['total_matches']}")
            print(f"全中期数: {result['summary']['full_matches']}")
            print(f"部分匹配期数: {result['summary']['partial_matches']}")
            print(f"匹配率: {result['summary']['match_rate']}%")
            print(f"详细结果数量: {len(result['details'])}")
            print(f"分布统计: {result['distribution']}")

            # 显示前5个详细结果
            print("\n前5个详细结果:")
            for i, detail in enumerate(result['details'][:5]):
                print(f"期号 {detail['period']}: 匹配{detail['match_count']}个号码 ({detail['match_rate']}%)")
                if detail['matched_numbers']:
                    print(f"  匹配号码: {detail['matched_numbers']}")

            # 测试筛选逻辑
            print("\n测试筛选逻辑:")
            for min_matches in [0, 1, 2, 3]:
                filtered = [d for d in result['details'] if d['match_count'] >= min_matches]
                total_matches = sum(1 for d in filtered if d['match_count'] > 0)
                full_matches = sum(1 for d in filtered if d['match_count'] == len(test_data['numbers']))
                partial_matches = total_matches - full_matches
                match_rate = round((total_matches / len(filtered)) * 100, 1) if len(filtered) > 0 else 0

                print(f"  最少{min_matches}个匹配: {len(filtered)}期, 总匹配{total_matches}期, 全中{full_matches}期, 部分匹配{partial_matches}期, 匹配率{match_rate}%")
        else:
            print(f"API测试失败: {response.text}")
            
    except Exception as e:
        print(f"测试出错: {e}")

def test_basic_apis():
    """测试基础API"""
    apis = [
        "http://localhost:8000/api/stats",
        "http://localhost:8000/api/frequency?limit=10",
        "http://localhost:8000/api/results?limit=3"
    ]
    
    for api in apis:
        try:
            response = requests.get(api)
            print(f"{api}: {response.status_code}")
        except Exception as e:
            print(f"{api}: 错误 - {e}")

if __name__ == "__main__":
    print("测试基础API...")
    test_basic_apis()
    
    print("\n测试号码分析API...")
    test_analyze_numbers()

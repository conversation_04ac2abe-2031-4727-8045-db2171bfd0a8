#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境启动文件
"""

import os
import sys
import logging
import uvicorn
from config import get_config

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging(config):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format=config.LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('app.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    # 获取配置
    env = os.getenv('ENVIRONMENT', 'production')
    config = get_config(env)
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    logger.info(f"启动 {config.APP_NAME} v{config.VERSION}")
    logger.info(f"环境: {env}")
    logger.info(f"调试模式: {config.DEBUG}")
    logger.info(f"服务地址: {config.HOST}:{config.PORT}")
    logger.info(f"工作进程: {config.WORKERS}")
    
    # 检查数据文件
    if not os.path.exists(config.DATA_FILE):
        logger.warning(f"数据文件 {config.DATA_FILE} 不存在")
        logger.info("建议先运行爬虫获取数据: python happy8_crawler.py")
    
    try:
        # 启动服务
        uvicorn.run(
            "app:app",
            host=config.HOST,
            port=config.PORT,
            workers=config.WORKERS if not config.DEBUG else 1,
            reload=config.DEBUG,
            log_level=config.LOG_LEVEL.lower(),
            access_log=True,
            loop="auto"
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

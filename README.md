# 快乐8数据分析系统

这是一个完整的福彩快乐8历史开奖数据分析系统，包含数据爬取、分析和可视化Web界面。

## 🌟 功能特点

- 🎯 爬取最近500期快乐8开奖数据
- 📊 支持CSV和JSON两种数据格式输出
- 🔍 包含数据分析功能
- 🛡️ 具备错误处理和重试机制
- 📈 提供号码频率统计和趋势分析
- 🌐 **Web可视化界面**
- 📱 **响应式设计，支持移动端**
- 📊 **交互式图表展示**
- 🔄 **实时数据刷新**
- 🎨 **美观的用户界面**

## 📁 文件说明

### 核心文件
- `app.py` - **FastAPI Web服务主程序**
- `data_service.py` - **数据服务模块**
- `happy8_crawler.py` - 数据爬虫脚本
- `analyze_data.py` - 命令行数据分析脚本
- `start_server.py` - **一键启动脚本**

### 配置文件
- `requirements.txt` - 依赖包列表

### 数据文件
- `happy8_data.csv` - 爬取的数据（CSV格式）
- `happy8_data.json` - 爬取的数据（JSON格式）

### 前端文件
- `templates/index.html` - 主页面模板
- `templates/error.html` - 错误页面模板
- `static/` - 静态资源目录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
python start_server.py
```

这个脚本会：
- 自动检查依赖包
- 检查数据文件（如果没有会提示是否爬取）
- 启动Web服务
- 自动打开浏览器

### 方法二：手动启动

#### 1. 爬取数据

```bash
python happy8_crawler.py
```

#### 2. 启动Web服务

```bash
python app.py
```

#### 3. 访问Web界面

打开浏览器访问：http://localhost:8000

### 方法三：命令行分析

```bash
python analyze_data.py
```

这将在命令行中显示数据分析结果。

## 数据格式

### CSV格式
```csv
period,numbers,numbers_str,date
2024144,"['01', '04', '05', '07', '08']","01 04 05 07 08",2024-05-23
```

### JSON格式
```json
{
  "period": "2024144",
  "numbers": ["01", "04", "05", "07", "08"],
  "numbers_str": "01 04 05 07 08",
  "date": "2024-05-23"
}
```

## 数据字段说明

- `period`: 期号（7位数字，如2024144）
- `numbers`: 开奖号码列表
- `numbers_str`: 开奖号码字符串（空格分隔）
- `date`: 开奖日期（YYYY-MM-DD格式）

## 🌐 Web界面功能

### 主要功能模块

1. **📊 数据概览**
   - 总期数统计
   - 数据时间跨度
   - 最新/最早期号

2. **🏆 最新开奖结果**
   - 显示最近5期开奖号码
   - 美观的号码展示
   - 期号和日期信息

3. **📈 可视化图表**
   - **号码频率柱状图**: 显示最热门的20个号码
   - **趋势分析图**: 最近50期热门号码趋势
   - **分布统计图**: 每期开出号码数量分布
   - **热力图**: 80个号码的频率热力图

4. **🔢 详细数据**
   - 最热门号码TOP 20
   - 最近50期热门号码TOP 15
   - 实时数据更新

5. **🔄 数据管理**
   - 一键刷新数据功能
   - 自动重新爬取最新数据
   - 实时状态反馈

### API接口

系统提供完整的RESTful API：

- `GET /api/stats` - 基本统计信息
- `GET /api/frequency` - 号码频率数据
- `GET /api/trends` - 趋势分析数据
- `GET /api/results` - 最新开奖结果
- `GET /api/distribution` - 号码分布数据
- `GET /api/charts/*` - 各种图表数据
- `POST /api/refresh` - 刷新数据
- `GET /api/health` - 健康检查

API文档地址：http://localhost:8000/docs

## 爬取结果示例

成功爬取500期数据后，你将看到类似以下的输出：

```
==================================================
福彩快乐8开奖数据爬虫
==================================================
开始爬取最近500期快乐8开奖数据...
页面获取成功，开始解析数据...
找到表格，开始解析...
找到 501 行数据
解析期号: 2024144, 号码: 01 04 05 07 08 09 10 11 13 18 24 27 43 48 62 66 71 75 76 78
...
成功解析 500 期数据
数据已保存到 happy8_data.csv
数据已保存到 happy8_data.json

爬取完成！
总共获取 500 期数据
最新期号: 2024144
最早期号: 2023348
```

## 数据分析结果示例

运行分析脚本后，你将看到：

```
快乐8开奖数据分析
==================================================
成功加载 500 期开奖数据

最热门的20个号码:
号码 02: 出现 223 次, 频率  44.6%
号码 01: 出现 216 次, 频率  43.2%
号码 03: 出现 168 次, 频率  33.6%
...

最近50期最热门的15个号码:
号码 04: 出现 20 次, 频率  40.0%
号码 56: 出现 19 次, 频率  38.0%
...
```

## 🛠️ 技术实现

### 后端技术栈
- **Web框架**: FastAPI - 高性能异步Web框架
- **数据处理**: pandas - 数据分析和处理
- **可视化**: Plotly - 交互式图表生成
- **模板引擎**: Jinja2 - HTML模板渲染
- **ASGI服务器**: Uvicorn - 高性能异步服务器

### 前端技术栈
- **UI框架**: Bootstrap 5 - 响应式UI组件
- **图表库**: Plotly.js - 交互式图表展示
- **图标**: Font Awesome - 矢量图标库
- **样式**: 自定义CSS + 渐变设计

### 数据处理
- **数据源**: 新浪彩票快乐8走势图页面
- **解析方式**: BeautifulSoup HTML解析
- **数据存储**: CSV和JSON格式
- **错误处理**: 完整的异常处理和重试机制

### 架构设计
- **MVC模式**: 清晰的模型-视图-控制器分离
- **RESTful API**: 标准的REST接口设计
- **模块化**: 数据服务、Web服务分离
- **可扩展性**: 易于添加新功能和图表类型

## 注意事项

1. **数据准确性**: 数据来源于第三方网站，仅供参考
2. **爬取频率**: 建议适当控制爬取频率，避免对服务器造成压力
3. **网络依赖**: 需要稳定的网络连接
4. **页面变化**: 如果源网站页面结构发生变化，可能需要更新解析逻辑
5. **法律合规**: 请确保爬取行为符合相关法律法规和网站使用条款

## 自定义配置

你可以修改 `happy8_crawler.py` 中的参数来自定义爬取行为：

```python
# 修改爬取期数（默认500期）
crawler.run(1000)  # 爬取1000期

# 修改输出文件名
crawler.save_to_csv('my_data.csv')
crawler.save_to_json('my_data.json')
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用VPN或代理

2. **解析失败**
   - 检查源网站是否可访问
   - 确认页面结构是否发生变化

3. **依赖包错误**
   - 重新安装依赖：`pip install -r requirements.txt`

4. **数据不完整**
   - 检查网络稳定性
   - 重新运行爬虫脚本

## 🚀 生产环境部署

### Docker部署（推荐）

#### 1. 快速部署
```bash
# 克隆项目
git clone <your-repo-url>
cd happy8

# 一键Docker部署
chmod +x deploy.sh
./deploy.sh docker
```

#### 2. 手动Docker部署
```bash
# 构建镜像
docker build -t happy8-analysis .

# 运行容器
docker run -d \
  --name happy8-app \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -e ENVIRONMENT=docker \
  -e WORKERS=2 \
  happy8-analysis

# 或使用docker-compose
docker-compose up -d
```

#### 3. 查看服务状态
```bash
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 传统部署

#### 1. 生产环境部署
```bash
# 安装依赖
pip install -r requirements.txt

# 生产环境启动
python main.py
```

#### 2. 使用部署脚本
```bash
# 开发环境
./deploy.sh dev

# 生产环境
./deploy.sh prod

# 查看状态
./deploy.sh status

# 停止服务
./deploy.sh stop
```

### Nginx反向代理（可选）

如果需要使用域名和SSL，可以配置Nginx：

```bash
# 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/happy8
sudo ln -s /etc/nginx/sites-available/happy8 /etc/nginx/sites-enabled/

# 重启Nginx
sudo systemctl restart nginx
```

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| ENVIRONMENT | development | 运行环境 (development/production/docker) |
| HOST | 0.0.0.0 | 监听地址 |
| PORT | 8000 | 监听端口 |
| WORKERS | 2 | 工作进程数 |
| CACHE_TIMEOUT | 300 | 缓存超时时间(秒) |

### 性能优化

#### 1. 系统要求
- **最低配置**: 1核CPU, 512MB内存
- **推荐配置**: 2核CPU, 1GB内存
- **存储空间**: 至少100MB

#### 2. 性能特性
- ✅ **内存缓存**: 5-10分钟图表缓存
- ✅ **异步处理**: FastAPI异步框架
- ✅ **数据优化**: pandas数据处理优化
- ✅ **静态资源**: CDN加速支持
- ✅ **健康检查**: 自动故障检测

#### 3. 监控和日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看访问日志
docker-compose logs nginx

# 健康检查
curl http://localhost:8000/api/health
```

### 故障排除

#### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :8000

   # 修改端口
   export PORT=8001
   ```

2. **内存不足**
   ```bash
   # 减少工作进程
   export WORKERS=1

   # 或增加系统内存
   ```

3. **数据文件缺失**
   ```bash
   # 手动运行爬虫
   python happy8_crawler.py
   ```

4. **Docker构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a

   # 重新构建
   docker-compose build --no-cache
   ```

### 安全建议

1. **防火墙配置**
   ```bash
   # 只开放必要端口
   ufw allow 80
   ufw allow 443
   ufw deny 8000  # 如果使用Nginx代理
   ```

2. **SSL证书**
   ```bash
   # 使用Let's Encrypt
   certbot --nginx -d your-domain.com
   ```

3. **定期更新**
   ```bash
   # 更新依赖
   pip install -r requirements.txt --upgrade

   # 重启服务
   ./deploy.sh stop
   ./deploy.sh docker
   ```

## 免责声明

本工具仅用于学习和研究目的，爬取的数据仅供参考。彩票具有随机性，任何基于历史数据的分析都不能保证未来结果。请理性购彩，量力而行。

## 许可证

MIT License

## 更新日志

- v1.0.0: 初始版本，支持爬取500期快乐8数据
- 包含基础数据分析功能
- 支持CSV和JSON输出格式
- 添加Web可视化界面
- 支持Docker部署
- 性能优化和缓存机制

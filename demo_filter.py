#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示筛选功能的效果
"""

import requests
import json

def demo_filter_functionality():
    """演示筛选功能"""
    url = "http://localhost:8000/api/analyze-numbers"
    
    # 测试数据
    test_data = {
        "numbers": [1, 15, 30, 45, 60],
        "periods": 100
    }
    
    print("=" * 60)
    print("快乐8号码分析 - 筛选功能演示")
    print("=" * 60)
    print(f"选择号码: {test_data['numbers']}")
    print(f"分析期数: {test_data['periods']}")
    print()
    
    try:
        response = requests.post(url, json=test_data)
        if response.status_code == 200:
            result = response.json()
            
            print("原始统计数据:")
            print(f"  总匹配期数: {result['summary']['total_matches']}")
            print(f"  全中期数: {result['summary']['full_matches']}")
            print(f"  部分匹配期数: {result['summary']['partial_matches']}")
            print(f"  匹配率: {result['summary']['match_rate']}%")
            print()
            
            print("分布统计:")
            for match_count, count in result['distribution'].items():
                print(f"  匹配{match_count}个号码: {count}期")
            print()
            
            # 模拟不同筛选条件的效果
            print("筛选效果演示:")
            print("-" * 40)
            
            for min_matches in range(0, 4):
                # 筛选数据
                filtered_details = [d for d in result['details'] if d['match_count'] >= min_matches]
                
                # 计算筛选后的统计
                total_matches = sum(1 for d in filtered_details if d['match_count'] > 0)
                full_matches = sum(1 for d in filtered_details if d['match_count'] == len(test_data['numbers']))
                partial_matches = total_matches - full_matches
                match_rate = round((total_matches / len(filtered_details)) * 100, 1) if len(filtered_details) > 0 else 0
                
                print(f"筛选条件: 至少匹配{min_matches}个号码")
                print(f"  显示期数: {len(filtered_details)}")
                print(f"  总匹配期数: {total_matches}")
                print(f"  全中期数: {full_matches}")
                print(f"  [界面只显示: 总匹配期数 + 全中期数 + 分布图表]")
                
                # 显示前3个结果
                if len(filtered_details) > 0:
                    print("  前3个结果:")
                    for i, detail in enumerate(filtered_details[:3]):
                        print(f"    期号{detail['period']}: 匹配{detail['match_count']}个 -> {detail['matched_numbers']}")
                print()
                
        else:
            print(f"API请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"请求出错: {e}")

if __name__ == "__main__":
    demo_filter_functionality()

version: '3.8'

services:
  happy8-app:
    build: .
    container_name: happy8-analysis
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=docker
      - PORT=8000
      - WORKERS=2
      - CACHE_TIMEOUT=600
    volumes:
      # 挂载数据目录，持久化数据文件
      - ./data:/app/data
      # 挂载日志目录
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - happy8-network

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: happy8-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    depends_on:
      - happy8-app
    restart: unless-stopped
    networks:
      - happy8-network

networks:
  happy8-network:
    driver: bridge

volumes:
  happy8-data:
    driver: local

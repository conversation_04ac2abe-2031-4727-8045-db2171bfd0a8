#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8开奖数据分析脚本
分析爬取到的500期开奖数据
"""

import pandas as pd
from collections import Counter

def load_data():
    """加载开奖数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv('happy8_data.csv')
        print(f"成功加载 {len(df)} 期开奖数据")
        return df
    except FileNotFoundError:
        print("数据文件不存在，请先运行爬虫脚本")
        return None

def analyze_number_frequency(df):
    """分析号码出现频率"""
    print("\n" + "="*50)
    print("号码出现频率分析")
    print("="*50)
    
    # 统计所有号码出现次数
    all_numbers = []
    for _, row in df.iterrows():
        numbers = eval(row['numbers'])  # 将字符串转换为列表
        all_numbers.extend([int(num) for num in numbers])
    
    # 计算频率
    frequency = Counter(all_numbers)
    
    # 显示最热门的20个号码
    print("最热门的20个号码:")
    for num, count in frequency.most_common(20):
        percentage = (count / len(df)) * 100
        print(f"号码 {num:02d}: 出现 {count:3d} 次, 频率 {percentage:5.1f}%")
    
    print("\n最冷门的20个号码:")
    for num, count in frequency.most_common()[-20:]:
        percentage = (count / len(df)) * 100
        print(f"号码 {num:02d}: 出现 {count:3d} 次, 频率 {percentage:5.1f}%")
    
    return frequency

def analyze_number_distribution(df):
    """分析号码分布"""
    print("\n" + "="*50)
    print("号码分布分析")
    print("="*50)
    
    # 统计每期开出的号码数量
    numbers_per_period = []
    for _, row in df.iterrows():
        numbers = eval(row['numbers'])
        numbers_per_period.append(len(numbers))
    
    print(f"每期开出号码数量统计:")
    print(f"最少: {min(numbers_per_period)} 个")
    print(f"最多: {max(numbers_per_period)} 个")
    print(f"平均: {sum(numbers_per_period)/len(numbers_per_period):.1f} 个")
    print(f"标准快乐8应该每期开出20个号码")
    
    # 统计号码数量分布
    count_distribution = Counter(numbers_per_period)
    print(f"\n号码数量分布:")
    for count, freq in sorted(count_distribution.items()):
        print(f"{count:2d}个号码: {freq:3d}期")

def analyze_recent_trends(df):
    """分析最近趋势"""
    print("\n" + "="*50)
    print("最近趋势分析")
    print("="*50)
    
    # 分析最近50期的数据
    recent_df = df.head(50)
    print(f"分析最近 {len(recent_df)} 期数据:")
    
    # 统计最近50期的热门号码
    recent_numbers = []
    for _, row in recent_df.iterrows():
        numbers = eval(row['numbers'])
        recent_numbers.extend([int(num) for num in numbers])
    
    recent_frequency = Counter(recent_numbers)
    
    print("\n最近50期最热门的15个号码:")
    for num, count in recent_frequency.most_common(15):
        percentage = (count / len(recent_df)) * 100
        print(f"号码 {num:02d}: 出现 {count:2d} 次, 频率 {percentage:5.1f}%")



def show_sample_data(df):
    """显示样本数据"""
    print("\n" + "="*50)
    print("样本数据展示")
    print("="*50)
    
    print("最新5期开奖结果:")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        numbers = eval(row['numbers'])
        numbers_str = ' '.join([f"{int(num):02d}" for num in numbers])
        print(f"期号: {row['period']}, 日期: {row['date']}")
        print(f"开奖号码: {numbers_str}")
        print(f"共{len(numbers)}个号码")
        print("-" * 40)

def main():
    """主函数"""
    print("快乐8开奖数据分析")
    print("="*50)
    
    # 加载数据
    df = load_data()
    if df is None:
        return
    
    # 显示基本信息
    print(f"数据概览:")
    print(f"总期数: {len(df)}")
    print(f"数据列: {list(df.columns)}")
    
    # 各种分析
    show_sample_data(df)
    analyze_number_distribution(df)
    frequency = analyze_number_frequency(df)
    analyze_recent_trends(df)
    
    print("\n" + "="*50)
    print("分析完成！")
    print("="*50)
    print("说明:")
    print("1. 数据来源于新浪彩票网站的快乐8走势图")
    print("2. 每期快乐8从01-80中开出20个号码")
    print("3. 部分期次可能因为解析问题导致号码数量不是20个")
    print("4. 数据仅供参考，彩票具有随机性")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据分析系统启动脚本
"""

import os
import webbrowser
import time

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'fastapi', 'uvicorn', 'pandas', 'plotly', 'jinja2'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_data_file():
    """检查数据文件是否存在"""
    if not os.path.exists('happy8_data.csv'):
        print("警告: 数据文件不存在")
        print("是否要先运行爬虫获取数据? (y/n): ", end='')
        
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            print("正在运行爬虫获取数据...")
            try:
                from happy8_crawler import Happy8Crawler
                crawler = Happy8Crawler()
                crawler.run(500)
                print("数据获取完成!")
                return True
            except Exception as e:
                print(f"数据获取失败: {e}")
                return False
        else:
            print("将使用空数据启动服务")
            return True
    
    return True

def start_server():
    """启动Web服务"""
    print("=" * 60)
    print("🎯 快乐8数据分析系统")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据文件
    if not check_data_file():
        return
    
    print("\n🚀 启动Web服务...")
    print("📊 服务地址: http://localhost:8000")
    print("📈 API文档: http://localhost:8000/docs")
    print("🔧 健康检查: http://localhost:8000/api/health")
    print("\n按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 启动服务
        import uvicorn
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:8000')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动服务器
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    start_server()

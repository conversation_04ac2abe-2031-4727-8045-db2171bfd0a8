#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩快乐8开奖数据爬虫
爬取最近500期的快乐8开奖结果
"""

import requests
import json
import time
import re

from bs4 import BeautifulSoup
import pandas as pd
from typing import List, Dict

class Happy8Crawler:
    def __init__(self):
        self.base_url = "https://view.lottery.sina.com.cn/lotto/pc_zst/index"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def get_lottery_data(self, period_count: int = 500) -> List[Dict]:
        """
        获取快乐8开奖数据

        Args:
            period_count: 要获取的期数，默认500期

        Returns:
            包含开奖数据的列表
        """
        print(f"开始爬取最近{period_count}期快乐8开奖数据...")

        # 构建请求参数
        params = {
            'lottoType': 'kl8',
            'actionType': 'chzs',
            'type': str(period_count),  # 期数
            'dpc': '1'
        }

        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            print("页面获取成功，开始解析数据...")
            lottery_data = self._parse_lottery_data(response.text)



            return lottery_data

        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []
    
    def _parse_lottery_data(self, html_content: str) -> List[Dict]:
        """
        解析HTML页面，提取开奖数据

        Args:
            html_content: HTML页面内容

        Returns:
            解析后的开奖数据列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        lottery_data = []

        # 保存HTML内容用于调试
        with open('debug_page.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("页面内容已保存到 debug_page.html")

        # 查找包含开奖数据的表格
        table = soup.find('table')
        if not table:
            print("未找到table标签，查找其他可能的容器...")
            # 尝试查找其他可能包含数据的元素
            containers = soup.find_all(['div', 'section'], class_=re.compile(r'table|data|result'))
            print(f"找到 {len(containers)} 个可能的数据容器")
            if containers:
                table = containers[0]
            else:
                return self._parse_alternative_method(soup)

        print(f"找到表格，开始解析...")

        # 查找所有数据行
        rows = table.find_all('tr')
        print(f"找到 {len(rows)} 行数据")

        for idx, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            print(f"第{idx+1}行有 {len(cells)} 个单元格")

            if len(cells) < 10:  # 至少要有期号和一些数据
                continue

            # 提取期号
            period_cell = cells[0]
            period_text = period_cell.get_text(strip=True)
            print(f"期号文本: '{period_text}'")

            # 验证期号格式 (例如: 2025144)
            if not re.match(r'^\d{7}$', period_text):
                print(f"期号格式不匹配: {period_text}")
                continue

            # 提取开奖号码 (从第2列开始的80列中找出中奖号码)
            winning_numbers = []
            for i in range(1, min(81, len(cells))):  # 号码01-80
                cell = cells[i]
                cell_text = cell.get_text(strip=True)

                # 检查是否包含中奖号码（通常用特殊样式标记）
                if self._is_winning_number(cell, i):
                    winning_numbers.append(f"{i:02d}")
                    print(f"找到中奖号码: {i:02d}")

            print(f"期号 {period_text} 找到 {len(winning_numbers)} 个中奖号码")

            # 快乐8每期开出20个号码，如果超过20个就从后往前取20个
            if len(winning_numbers) >= 15:  # 至少要有15个号码才认为是有效数据
                if len(winning_numbers) > 20:
                    # 如果超过20个号码，从后往前取20个
                    winning_numbers = winning_numbers[-20:]
                    print(f"期号 {period_text} 号码超过20个，已截取最后20个")

                lottery_data.append({
                    'period': period_text,
                    'numbers': winning_numbers,
                    'numbers_str': ' '.join(winning_numbers)
                })
                print(f"解析期号: {period_text}, 号码: {' '.join(winning_numbers)} (共{len(winning_numbers)}个)")

        print(f"成功解析 {len(lottery_data)} 期数据")
        return lottery_data
    
    def _parse_alternative_method(self, soup: BeautifulSoup) -> List[Dict]:
        """
        备用解析方法，直接从页面文本中提取数据
        """
        print("使用备用解析方法...")
        lottery_data = []
        
        # 查找所有可能包含期号的元素
        period_pattern = r'(\d{7})'
        text_content = soup.get_text()
        
        # 这里可以添加更复杂的解析逻辑
        # 由于页面结构可能复杂，我们先返回空列表，后续可以完善
        
        return lottery_data
    
    def _is_winning_number(self, cell, number: int) -> bool:
        """
        判断某个号码位置是否为中奖号码
        
        Args:
            cell: 表格单元格元素
            number: 号码位置 (1-80)
            
        Returns:
            是否为中奖号码
        """
        # 检查单元格内容和样式
        cell_text = cell.get_text(strip=True)
        
        # 如果单元格显示的是号码本身，说明是中奖号码
        if cell_text == f"{number:02d}" or cell_text == str(number):
            return True
            
        # 检查是否有特殊的CSS类名或样式
        cell_class = cell.get('class', [])
        if any('win' in cls.lower() or 'select' in cls.lower() or 'hit' in cls.lower() 
               for cls in cell_class):
            return True
            
        # 检查是否有特殊的背景色或其他样式标记
        style = cell.get('style', '')
        if 'background' in style.lower() or 'color' in style.lower():
            return True
            
        return False




    
    def save_to_csv(self, data: List[Dict], filename: str = 'happy8_data.csv'):
        """
        保存数据到CSV文件
        """
        if not data:
            print("没有数据可保存")
            return
            
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到 {filename}")
    
    def save_to_json(self, data: List[Dict], filename: str = 'happy8_data.json'):
        """
        保存数据到JSON文件
        """
        if not data:
            print("没有数据可保存")
            return
            
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到 {filename}")
    
    def run(self, period_count: int = 500):
        """
        运行爬虫
        
        Args:
            period_count: 要爬取的期数
        """
        print("=" * 50)
        print("福彩快乐8开奖数据爬虫")
        print("=" * 50)
        
        # 获取数据
        data = self.get_lottery_data(period_count)
        
        if data:
            # 保存数据
            self.save_to_csv(data)
            self.save_to_json(data)
            
            # 显示统计信息
            print(f"\n爬取完成！")
            print(f"总共获取 {len(data)} 期数据")
            if data:
                print(f"最新期号: {data[0]['period']}")
                print(f"最早期号: {data[-1]['period']}")
        else:
            print("未获取到任何数据，请检查网络连接或页面结构是否发生变化")

def main():
    """
    主函数
    """
    crawler = Happy8Crawler()
    
    # 爬取最近500期数据
    crawler.run(500)
    
    # 添加延时避免频繁请求
    time.sleep(2)

if __name__ == "__main__":
    main()

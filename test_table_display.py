#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格显示效果
"""

import requests

def test_table_display():
    """测试表格显示"""
    url = "http://localhost:8000/api/analyze-numbers"
    
    # 测试数据
    test_data = {
        "numbers": [1, 15, 30],
        "periods": 20
    }
    
    print("=" * 60)
    print("表格显示效果测试")
    print("=" * 60)
    print(f"选择号码: {test_data['numbers']}")
    print(f"分析期数: {test_data['periods']}")
    print()
    
    try:
        response = requests.post(url, json=test_data)
        if response.status_code == 200:
            result = response.json()
            
            print("表格结构:")
            print("期号 | 开奖号码(匹配号码显示为红色) | 匹配数量")
            print("-" * 60)
            
            # 显示前10个结果
            for i, detail in enumerate(result['details'][:10]):
                period = detail['period']
                numbers = detail['numbers']
                matched_numbers = detail['matched_numbers']
                match_count = detail['match_count']
                
                # 模拟前端显示效果
                numbers_display = []
                for num in numbers:
                    if num in matched_numbers:
                        numbers_display.append(f"[红色]{num:02d}")
                    else:
                        numbers_display.append(f"{num:02d}")
                
                numbers_str = " ".join(numbers_display)
                print(f"{period} | {numbers_str} | {match_count}")
            
            print()
            print("说明:")
            print("- [红色]XX 表示在前端会以红色背景显示的匹配号码")
            print("- 其他号码以蓝色背景显示")
            print("- 删除了'匹配号码'和'匹配率'列，表格更简洁")
                
        else:
            print(f"API请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"请求出错: {e}")

if __name__ == "__main__":
    test_table_display()

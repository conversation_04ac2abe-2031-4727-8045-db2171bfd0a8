# 更新日志

## [1.1.0] - 2024-12-19

### 新增功能
- ✅ **数据刷新缓存机制**: 添加10分钟刷新限制，防止频繁爬取数据
- ✅ **刷新状态API**: 新增 `/api/refresh/status` 接口，实时显示刷新状态
- ✅ **智能刷新按钮**: 前端按钮显示倒计时，用户体验更佳
- ✅ **配置化缓存时间**: 通过配置文件统一管理缓存时间

### 修复问题
- 🔧 **期号日期对应关系**: 修复期号和开奖日期的计算逻辑
- 🔧 **缓存时间优化**: 将缓存时间从5分钟调整为10分钟
- 🔧 **错误处理改进**: 更好的错误提示和状态码处理

### 技术改进
- 📈 **性能优化**: 数据缓存机制减少重复计算
- 🛡️ **防护机制**: 防止恶意频繁刷新数据
- 🎨 **用户体验**: 实时状态显示和友好的错误提示

### API变更
- **新增**: `GET /api/refresh/status` - 获取刷新状态
- **修改**: `POST /api/refresh` - 返回格式包含更多状态信息
- **状态码**: 429 - 请求过于频繁时返回

### 配置变更
```python
# config.py
CACHE_TIMEOUT = 600      # 10分钟缓存
REFRESH_TIMEOUT = 600    # 10分钟刷新限制
```

### 前端改进
- 刷新按钮状态管理
- 倒计时显示
- 错误提示优化
- 自动状态检查

### 使用说明

#### 刷新限制
- 数据刷新间隔限制为10分钟
- 频繁刷新时会显示剩余等待时间
- 刷新按钮会自动显示倒计时

#### 缓存机制
- 图表数据缓存10分钟
- 统计数据缓存10分钟
- 自动清理过期缓存

#### 测试方法
```bash
# 运行缓存测试
python test_cache.py
```

### 已知问题
- 期号日期对应关系仍为估算值，实际开奖时间可能有差异
- 缓存时间可根据实际需求调整

### 下一版本计划
- [ ] 更精确的期号日期对应关系
- [ ] 数据库存储支持
- [ ] 更多数据分析功能
- [ ] 移动端适配优化

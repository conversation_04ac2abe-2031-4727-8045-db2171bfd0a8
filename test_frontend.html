<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>快乐8前端功能测试</h1>
    
    <div class="test-section">
        <h3>测试1: 基础API连接</h3>
        <button onclick="testBasicAPI()">测试基础API</button>
        <div id="basicResult"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 号码分析API</h3>
        <button onclick="testAnalyzeAPI()">测试号码分析</button>
        <div id="analyzeResult"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 前端页面访问</h3>
        <button onclick="openMainPage()">打开主页面</button>
        <p>点击按钮将在新窗口打开主页面</p>
    </div>

    <script>
        async function testBasicAPI() {
            const resultDiv = document.getElementById('basicResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('http://localhost:8000/api/stats');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<span class="success">✓ 基础API正常</span><br>
                        总期数: ${data.total_periods}<br>
                        最新期号: ${data.latest_period}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ API响应错误: ${response.status}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 连接失败: ${error.message}</span>`;
            }
        }
        
        async function testAnalyzeAPI() {
            const resultDiv = document.getElementById('analyzeResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('http://localhost:8000/api/analyze-numbers', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        numbers: [1, 15, 30, 45, 60],
                        periods: 50
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<span class="success">✓ 号码分析API正常</span><br>
                        总匹配期数: ${data.summary.total_matches}<br>
                        匹配率: ${data.summary.match_rate}%<br>
                        分布统计: ${JSON.stringify(data.distribution)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ API响应错误: ${response.status}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 连接失败: ${error.message}</span>`;
            }
        }
        
        function openMainPage() {
            window.open('http://localhost:8000', '_blank');
        }
    </script>
</body>
</html>
